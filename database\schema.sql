-- Create a table for public profiles
create table profiles (
  id uuid references auth.users on delete cascade not null primary key,
  updated_at timestamp with time zone,
  username text unique,
  full_name text,
  avatar_url text,
  website text,

  constraint username_length check (char_length(username) >= 3)
);

-- Set up Row Level Security (RLS)
-- See https://supabase.com/docs/guides/auth/row-level-security for more details.
alter table profiles
  enable row level security;

create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check (auth.uid() = id);

create policy "Users can update own profile." on profiles
  for update using (auth.uid() = id);

-- This trigger automatically creates a profile entry when a new user signs up via Supabase Auth.
-- See https://supabase.com/docs/guides/auth/managing-user-data#using-triggers for more details.
create function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$$ language plpgsql security definer;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Set up Storage!
insert into storage.buckets (id, name)
  values ('avatars', 'avatars');

-- Set up access controls for storage.
-- See https://supabase.com/docs/guides/storage#policy-examples for more details.
create policy "Avatar images are publicly accessible." on storage.objects
  for select using (bucket_id = 'avatars');

create policy "Anyone can upload an avatar." on storage.objects
  for insert with check (bucket_id = 'avatars');

create policy "Anyone can update their own avatar." on storage.objects
  for update using (auth.uid()::text = (storage.foldername(name))[1]);

-- Create agents table for savings agents
create table agents (
  id uuid default gen_random_uuid() primary key,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  name text not null,
  phone text not null,
  email text,
  is_active boolean default true,

  constraint agents_name_length check (char_length(name) >= 2),
  constraint agents_phone_length check (char_length(phone) >= 10)
);

-- Set up Row Level Security for agents
alter table agents enable row level security;

create policy "Agents are viewable by everyone." on agents
  for select using (is_active = true);

create policy "Only authenticated users can view all agents." on agents
  for select using (auth.role() = 'authenticated');

-- Create savings accounts table
create table savings_accounts (
  id uuid default gen_random_uuid() primary key,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  user_id uuid references auth.users on delete cascade not null,
  agent_id uuid references agents on delete set null,
  current_amount decimal(15,2) default 0.00 not null,
  target_amount decimal(15,2) default 0.00 not null,
  is_active boolean default true,

  constraint savings_accounts_amounts_positive check (current_amount >= 0 and target_amount >= 0),
  constraint savings_accounts_target_greater check (target_amount > 0)
);

-- Set up Row Level Security for savings accounts
alter table savings_accounts enable row level security;

create policy "Users can view their own savings accounts." on savings_accounts
  for select using (auth.uid() = user_id);

create policy "Users can insert their own savings accounts." on savings_accounts
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own savings accounts." on savings_accounts
  for update using (auth.uid() = user_id);

-- Create savings transactions table for tracking deposits/withdrawals
create table savings_transactions (
  id uuid default gen_random_uuid() primary key,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  savings_account_id uuid references savings_accounts on delete cascade not null,
  amount decimal(15,2) not null,
  transaction_type text not null check (transaction_type in ('deposit', 'withdrawal')),
  description text,
  processed_by_agent_id uuid references agents on delete set null,

  constraint savings_transactions_amount_positive check (amount > 0)
);

-- Set up Row Level Security for savings transactions
alter table savings_transactions enable row level security;

create policy "Users can view their own savings transactions." on savings_transactions
  for select using (
    exists (
      select 1 from savings_accounts
      where savings_accounts.id = savings_transactions.savings_account_id
      and savings_accounts.user_id = auth.uid()
    )
  );

-- Function to automatically create a savings account when a user signs up
create or replace function public.handle_new_user_savings()
returns trigger as $$
declare
  default_agent_id uuid;
begin
  -- Get a default agent (first active agent)
  select id into default_agent_id from agents where is_active = true limit 1;

  -- Create a default savings account
  insert into public.savings_accounts (user_id, agent_id, target_amount)
  values (new.id, default_agent_id, ********.00); -- Default target: 10 million IDR

  return new;
end;
$$ language plpgsql security definer;

-- Trigger to create savings account for new users
create trigger on_auth_user_created_savings
  after insert on auth.users
  for each row execute procedure public.handle_new_user_savings();

-- Insert some sample agents
insert into agents (name, phone, email) values
  ('Ibu Sari', '0812-3456-7890', '<EMAIL>'),
  ('Pak Budi', '0813-4567-8901', '<EMAIL>'),
  ('Ibu Rina', '0814-5678-9012', '<EMAIL>');

-- Function to update savings account balance after transaction
create or replace function public.update_savings_balance()
returns trigger as $$
begin
  if TG_OP = 'INSERT' then
    if NEW.transaction_type = 'deposit' then
      update savings_accounts
      set current_amount = current_amount + NEW.amount,
          updated_at = now()
      where id = NEW.savings_account_id;
    elsif NEW.transaction_type = 'withdrawal' then
      update savings_accounts
      set current_amount = current_amount - NEW.amount,
          updated_at = now()
      where id = NEW.savings_account_id;
    end if;
    return NEW;
  end if;
  return null;
end;
$$ language plpgsql security definer;

-- Trigger to update balance after transaction
create trigger on_savings_transaction_insert
  after insert on savings_transactions
  for each row execute procedure public.update_savings_balance();
