import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from './components/AuthForm';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    const { error } = await signIn(email, password);

    if (error) {
      Alert.alert('Login Error', error.message);
    } else {
      // Navigation will be handled by the auth context
      router.replace('/(tabs)');
    }
    setLoading(false);
  };

  return (
    <AuthForm
      title="Welcome Back"
      subtitle="Sign in to your account"
      email={email}
      password={password}
      loading={loading}
      buttonText="Sign In"
      loadingText="Signing In..."
      linkText="Don't have an account?"
      linkHref="/(auth)/register"
      linkButtonText="Sign Up"
      onEmailChange={setEmail}
      onPasswordChange={setPassword}
      onSubmit={handleLogin}
    />
  );
}


