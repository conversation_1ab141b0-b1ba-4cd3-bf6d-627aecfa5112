import { Hre<PERSON>, <PERSON> } from 'expo-router';
import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { authStyles } from '../styles';

interface AuthFormProps {
  title: string;
  subtitle: string;
  email: string;
  password: string;
  confirmPassword?: string;
  loading: boolean;
  buttonText: string;
  loadingText: string;
  linkText: string;
  linkHref: Href;
  linkButtonText: string;
  showConfirmPassword?: boolean;
  onEmailChange: (email: string) => void;
  onPasswordChange: (password: string) => void;
  onConfirmPasswordChange?: (password: string) => void;
  onSubmit: () => void;
}

export default function AuthForm({
  title,
  subtitle,
  email,
  password,
  confirmPassword,
  loading,
  buttonText,
  loadingText,
  linkText,
  linkHref,
  linkButtonText,
  showConfirmPassword = false,
  onEmailChange,
  onPasswordChange,
  onConfirmPasswordChange,
  onSubmit,
}: AuthFormProps) {
  return (
    <KeyboardAvoidingView
      style={authStyles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={authStyles.scrollContainer}>
        <View style={authStyles.formContainer}>
          <Text style={authStyles.title}>{title}</Text>
          <Text style={authStyles.subtitle}>{subtitle}</Text>

          <View style={authStyles.inputContainer}>
            <Text style={authStyles.label}>Email</Text>
            <TextInput
              style={authStyles.input}
              value={email}
              onChangeText={onEmailChange}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={authStyles.inputContainer}>
            <Text style={authStyles.label}>Password</Text>
            <TextInput
              style={authStyles.input}
              value={password}
              onChangeText={onPasswordChange}
              placeholder="Enter your password"
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          {showConfirmPassword && (
            <View style={authStyles.inputContainer}>
              <Text style={authStyles.label}>Confirm Password</Text>
              <TextInput
                style={authStyles.input}
                value={confirmPassword}
                onChangeText={onConfirmPasswordChange}
                placeholder="Confirm your password"
                secureTextEntry
                autoCapitalize="none"
              />
            </View>
          )}

          <TouchableOpacity
            style={[authStyles.button, loading && authStyles.buttonDisabled]}
            onPress={onSubmit}
            disabled={loading}
          >
            <Text style={authStyles.buttonText}>
              {loading ? loadingText : buttonText}
            </Text>
          </TouchableOpacity>

          <View style={authStyles.linkContainer}>
            <Text style={authStyles.linkText}>{linkText} </Text>
            <Link href={linkHref} style={authStyles.link}>
              <Text style={authStyles.linkTextBold}>{linkButtonText}</Text>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
