import { StyleSheet } from 'react-native';

// Shared styles for elderly-friendly design
export const elderlyFriendlyStyles = StyleSheet.create({
  // Large, readable text
  largeText: {
    fontSize: 20,
    fontWeight: '600',
  },
  mediumText: {
    fontSize: 18,
    fontWeight: '500',
  },
  smallText: {
    fontSize: 16,
    fontWeight: '400',
  },

  // High contrast colors
  primaryGreen: {
    color: '#2E7D32',
  },
  secondaryGreen: {
    color: '#4CAF50',
  },
  textDark: {
    color: '#333',
  },
  textMedium: {
    color: '#666',
  },
  textLight: {
    color: '#999',
  },

  // Card styles with good shadows
  card: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Button styles with good touch targets
  primaryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
    minHeight: 50,
  },
  dangerButton: {
    backgroundColor: '#FF5722',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
    minHeight: 50,
  },
});
